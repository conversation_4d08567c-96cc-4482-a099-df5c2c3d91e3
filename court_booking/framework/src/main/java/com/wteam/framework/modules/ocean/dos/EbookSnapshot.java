package com.wteam.framework.modules.ocean.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wteam.framework.common.mybatis.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-22
 */
@Data
@TableName("ebook_snapshot")
@Schema(description = "")
public class EbookSnapshot extends BaseEntity {

	@Schema(description = "")
	private String id;
	@Schema(description = "电子书id")
	private String ebookId;
	@Schema(description = "快照日期")
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
	private Date date;
	@Schema(description = "阅读数")
	private Integer viewCount;
	@Schema(description = "点赞数")
	private Integer voteCount;
	@Schema(description = "阅读增长")
	private Integer viewIncrease;
	@Schema(description = "点赞增长")
	private Integer voteIncrease;
}