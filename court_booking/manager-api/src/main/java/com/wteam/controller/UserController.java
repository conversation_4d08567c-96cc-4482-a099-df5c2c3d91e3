package com.wteam.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wteam.framework.common.enums.ResultCode;
import com.wteam.framework.common.enums.ResultUtil;
import com.wteam.framework.common.mybatis.util.PageUtil;
import com.wteam.framework.common.vo.PageVO;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.common.vo.SearchVO;
import com.wteam.framework.modules.ocean.dos.User;
import com.wteam.framework.modules.ocean.dos.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-22
 */
@RestController
@RequestMapping("staff/user")
@Tag(name = "用户管理类")
public class UserController {

    @Autowired
    private UserService userService;

    @GetMapping("page")
    @Operation(summary  = "分页")
    public ResultMessage<IPage<User>> pageUser(User entity,
                                                                SearchVO searchVo,
                                                                PageVO page) {
        IPage<User> data = userService.page(PageUtil.initPage(page), PageUtil.initWrapper(entity, searchVo));
        return ResultUtil.data(data);

    }

    @GetMapping("{id}")
    @Operation(summary  = "id查询")
    public ResultMessage<User> getUser(@PathVariable("id") Long id) {
        User data = userService.getById(id);

        return ResultUtil.data(data);
    }

    @PostMapping
    @Operation(summary  = "保存")
    public ResultMessage saveUser(@RequestBody User entity) {
        //效验数据
        return userService.save(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);

    }

    @PutMapping
    @Operation(summary  = "修改")
    public ResultMessage updateUser(@RequestBody User entity) {
        //效验数据
        return userService.updateById(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping("/{id}")
    @Operation(summary  = "删除")
    public ResultMessage deleteUser(@PathVariable String id) {
        //效验数据
        return userService.removeById(id) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping
    @Operation(summary  = "批量删除")
    @Parameters({
            @Parameter(name = "ids", description = "ID集合", required = true, schema = @Schema(type = "array",implementation = String.class),  in = ParameterIn.QUERY),
    })
    public ResultMessage deleteUsers(@RequestParam List<String> ids) {
        //效验数据
        return userService.removeBatchByIds(ids) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "根据登录名和密码验证用户")
    public ResultMessage<User> login(
            @RequestParam @Parameter(description = "登录名") String loginName,
            @RequestParam @Parameter(description = "密码") String password) {

        // 直接在Controller层使用QueryWrapper查询
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("login_name", loginName)
                .eq("password", password);

        User user = userService.getOne(queryWrapper);

        if (user != null) {
            return ResultUtil.data(user);
        } else {
            return ResultUtil.error(ResultCode.USERERROR);
        }
    }


}