package com.wteam.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wteam.framework.common.enums.ResultCode;
import com.wteam.framework.common.enums.ResultUtil;
import com.wteam.framework.common.mybatis.util.PageUtil;
import com.wteam.framework.common.vo.PageVO;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.common.vo.SearchVO;
import com.wteam.framework.modules.ocean.dos.Doc;
import com.wteam.framework.modules.ocean.dos.Ebook;
import com.wteam.framework.modules.ocean.dos.service.DocService;
import com.wteam.framework.modules.ocean.dos.service.EbookService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 电子书
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-22
 */
@RestController
@RequestMapping("staff/ebook")
@Tag(name = "电子书实体类后端接口")
public class EbookController {

    @Autowired
    private DocService docService;

    @Autowired
    private EbookService ebookService;

    @GetMapping("page")
    @Operation(summary  = "分页")
    public ResultMessage<IPage<Ebook>> pageEbook(Ebook entity,
                                            SearchVO searchVo,
                                            PageVO page) {
        IPage<Ebook> data = ebookService.page(PageUtil.initPage(page), PageUtil.initWrapper(entity, searchVo));
        return ResultUtil.data(data);

    }

    @GetMapping("{id}")
    @Operation(summary  = "id查询")
    public ResultMessage<Ebook> getEbook(@PathVariable("id") Long id) {
        Ebook data = ebookService.getById(id);

        return ResultUtil.data(data);
    }

    @GetMapping("/category2/{category2Id}/page")
    @Operation(summary = "根据二级分类ID分页查询电子书")
    public ResultMessage<IPage<Ebook>> getEbooksByCategory2IdPage(
            @PathVariable("category2Id") String category2Id,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {

        // 1. 分页查询该分类下的电子书
        Page<Ebook> page = new Page<>(current, size);
        QueryWrapper<Ebook> ebookQuery = new QueryWrapper<>();
        ebookQuery.eq("category2_id", category2Id);
        IPage<Ebook> ebookPage = ebookService.page(page, ebookQuery);

        // 2. 遍历每个电子书，统计文档数并更新
        ebookPage.getRecords().forEach(ebook -> {
            // 统计该电子书下的文档数量
            QueryWrapper<Doc> docQuery = new QueryWrapper<>();
            docQuery.eq("ebook_id", ebook.getId());
            int docCount = Math.toIntExact(docService.count(docQuery));

            // 更新电子书的文档数（只更新docCount字段）
            Ebook updateEbook = new Ebook();
            updateEbook.setId(ebook.getId());
            updateEbook.setDocCount(docCount);
            ebookService.updateById(updateEbook);

            // 同时更新返回对象中的文档数
            ebook.setDocCount(docCount);
        });

        return ResultUtil.data(ebookPage);
    }

    @PostMapping
    @Operation(summary  = "保存")
    public ResultMessage saveEbook(@RequestBody Ebook entity) {
        //效验数据
        return ebookService.save(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);

    }

    @PutMapping
    @Operation(summary  = "修改")
    public ResultMessage updateEbook(@RequestBody Ebook entity) {
        //效验数据
        return ebookService.updateById(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping("/{id}")
    @Operation(summary  = "删除")
    public ResultMessage deleteEbook(@PathVariable String id) {
        //效验数据
        return ebookService.removeById(id) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping
    @Operation(summary  = "批量删除")
    @Parameters({
            @Parameter(name = "ids", description = "ID集合", required = true, schema = @Schema(type = "array",implementation = String.class),  in = ParameterIn.QUERY),
    })
    public ResultMessage deleteEbooks(@RequestParam List<String> ids) {
        //效验数据
        return ebookService.removeBatchByIds(ids) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }


}