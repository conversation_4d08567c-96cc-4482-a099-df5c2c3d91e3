package com.wteam.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wteam.framework.common.enums.ResultCode;
import com.wteam.framework.common.enums.ResultUtil;
import com.wteam.framework.common.mybatis.util.PageUtil;
import com.wteam.framework.common.vo.PageVO;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.common.vo.SearchVO;
import com.wteam.framework.modules.ocean.dos.Doc;
import com.wteam.framework.modules.ocean.dos.service.DocService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-24
 */
@RestController
@RequestMapping("staff/doc")
@Tag(name = "海洋生物文档后端接口")
public class DocController {

    @Autowired
    private DocService docService;

    @GetMapping("page")
    @Operation(summary  = "分页")
    public ResultMessage<IPage<Doc>> pageDoc(Doc entity,
                                          SearchVO searchVo,
                                          PageVO page) {
        IPage<Doc> data = docService.page(PageUtil.initPage(page), PageUtil.initWrapper(entity, searchVo));
        return ResultUtil.data(data);

    }

    @GetMapping("{id}")
    @Operation(summary  = "id查询")
    public ResultMessage<Doc> getDoc(@PathVariable("id") Long id) {
        Doc data = docService.getById(id);

        return ResultUtil.data(data);
    }

    @PostMapping
    @Operation(summary  = "保存")
    public ResultMessage saveDoc(@RequestBody Doc entity) {
        //效验数据
        return docService.save(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);

    }

    @GetMapping("/ebook/{ebookId}/docs")
    @Operation(summary = "根据电子书ID分页查询文档", description = "通过电子书ID获取其下的文档列表（分页）")
    public ResultMessage<IPage<Doc>> getDocsByEbookIdPage(
            @PathVariable("ebookId") String ebookId,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {

        // 创建分页对象
        Page<Doc> page = new Page<>(current, size);

        // 构建查询条件（使用LambdaQueryWrapper更安全）
        LambdaQueryWrapper<Doc> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Doc::getEbookId, ebookId)
                .orderByAsc(Doc::getSort); // 按sort字段升序排列（可根据需求调整）

        // 执行分页查询
        IPage<Doc> data = docService.page(page, queryWrapper);

        return ResultUtil.data(data);
    }

    @PutMapping
    @Operation(summary  = "修改")
    public ResultMessage updateDoc(@RequestBody Doc entity) {
        //效验数据
        return docService.updateById(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping("/{id}")
    @Operation(summary  = "删除")
    public ResultMessage deleteDoc(@PathVariable String id) {
        //效验数据
        return docService.removeById(id) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping
    @Operation(summary  = "批量删除")
    @Parameters({
            @Parameter(name = "ids", description = "ID集合", required = true, schema = @Schema(type = "array",implementation = String.class),  in = ParameterIn.QUERY),
    })
    public ResultMessage deleteDocs(@RequestParam List<String> ids) {
        //效验数据
        return docService.removeBatchByIds(ids) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }


}