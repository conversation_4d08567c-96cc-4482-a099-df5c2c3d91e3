package com.wteam.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wteam.framework.common.enums.ResultCode;
import com.wteam.framework.common.enums.ResultUtil;
import com.wteam.framework.common.mybatis.util.PageUtil;
import com.wteam.framework.common.vo.PageVO;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.common.vo.SearchVO;
import com.wteam.framework.modules.ocean.dos.Content;
import com.wteam.framework.modules.ocean.dos.service.ContentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-22
 */
@RestController
@RequestMapping("staff/content")
@Tag(name = "富文本编辑器实体类后端接口")
public class ContentController {

    @Autowired
    private ContentService contentService;

    @GetMapping("page")
    @Operation(summary  = "分页")
    public ResultMessage<IPage<Content>> pageContent(Content entity,
                                                                SearchVO searchVo,
                                                                PageVO page) {
        IPage<Content> data = contentService.page(PageUtil.initPage(page), PageUtil.initWrapper(entity, searchVo));
        return ResultUtil.data(data);

    }

    @GetMapping("{id}")
    @Operation(summary  = "id查询")
    public ResultMessage<Content> getContent(@PathVariable("id") Long id) {
        Content data = contentService.getById(id);

        return ResultUtil.data(data);
    }

    @GetMapping("/byDoc/{docId}")
    @Operation(summary = "根据文档ID查询内容", description = "根据文档的唯一标识docId查询对应的内容")
    public ResultMessage<Content> getContentByDocId(
            @PathVariable @Parameter(description = "文档ID") String docId) {

        // 使用LambdaQueryWrapper构建查询条件
        Content content = contentService.lambdaQuery()
                .eq(Content::getDocId, docId)
                .one();

        if (content != null) {
            return ResultUtil.data(content);
        } else {
            return ResultUtil.error(ResultCode.CONTENT_DOC_ERROR);
        }
    }



    @PostMapping
    @Operation(summary  = "保存")
    public ResultMessage saveContent(@RequestBody Content entity) {
        //效验数据
        return contentService.save(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);

    }

    @PutMapping
    @Operation(summary  = "修改")
    public ResultMessage updateContent(@RequestBody Content entity) {
        //效验数据
        return contentService.updateById(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping("/{id}")
    @Operation(summary  = "删除")
    public ResultMessage deleteContent(@PathVariable String id) {
        //效验数据
        return contentService.removeById(id) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping
    @Operation(summary  = "批量删除")
    @Parameters({
            @Parameter(name = "ids", description = "ID集合", required = true, schema = @Schema(type = "array",implementation = String.class),  in = ParameterIn.QUERY),
    })
    public ResultMessage deleteContents(@RequestParam List<String> ids) {
        //效验数据
        return contentService.removeBatchByIds(ids) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }


}