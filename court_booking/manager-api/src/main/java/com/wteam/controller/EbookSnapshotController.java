package com.wteam.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wteam.framework.common.enums.ResultCode;
import com.wteam.framework.common.enums.ResultUtil;
import com.wteam.framework.common.mybatis.util.PageUtil;
import com.wteam.framework.common.vo.PageVO;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.common.vo.SearchVO;
import com.wteam.framework.modules.ocean.dos.EbookSnapshot;
import com.wteam.framework.modules.ocean.dos.service.EbookSnapshotService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * 
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-22
 */
@RestController
@RequestMapping("staff/ebooksnapshot")
@Tag(name = "仪表盘后端接口")
public class EbookSnapshotController {

    @Autowired
    private EbookSnapshotService ebookSnapshotService;

    @GetMapping("page")
    @Operation(summary  = "分页")
    public ResultMessage<IPage<EbookSnapshot>> pageEbookSnapshot(EbookSnapshot entity,
                                                    SearchVO searchVo,
                                                    PageVO page) {
        IPage<EbookSnapshot> data = ebookSnapshotService.page(PageUtil.initPage(page), PageUtil.initWrapper(entity, searchVo));
        return ResultUtil.data(data);

    }

    @GetMapping("{id}")
    @Operation(summary  = "id查询")
    public ResultMessage<EbookSnapshot> getEbookSnapshot(@PathVariable("id") Long id) {
        EbookSnapshot data = ebookSnapshotService.getById(id);

        return ResultUtil.data(data);
    }

    @PostMapping
    @Operation(summary  = "保存")
    public ResultMessage saveEbookSnapshot(@RequestBody EbookSnapshot entity) {
        //效验数据
        return ebookSnapshotService.save(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);

    }

    @PutMapping
    @Operation(summary  = "修改")
    public ResultMessage updateEbookSnapshot(@RequestBody EbookSnapshot entity) {
        //效验数据
        return ebookSnapshotService.updateById(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping("/{id}")
    @Operation(summary  = "删除")
    public ResultMessage deleteEbookSnapshot(@PathVariable String id) {
        //效验数据
        return ebookSnapshotService.removeById(id) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping
    @Operation(summary  = "批量删除")
    @Parameters({
            @Parameter(name = "ids", description = "ID集合", required = true, schema = @Schema(type = "array",implementation = String.class),  in = ParameterIn.QUERY),
    })
    public ResultMessage deleteEbookSnapshot(@RequestParam List<String> ids) {
        //效验数据
        return ebookSnapshotService.removeBatchByIds(ids) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @GetMapping("/ebook/{ebookId}")
    @Operation(summary = "根据电子书ID查询快照数据并计算增长")
    @Parameter(name = "ebookId", description = "电子书ID", required = true, in = ParameterIn.PATH)
    public ResultMessage<List<EbookSnapshot>> getSnapshotsByEbookId(@PathVariable("ebookId") String ebookId) {
        // 查询指定ebookId的所有快照数据
        QueryWrapper<EbookSnapshot> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ebook_id", ebookId);
        queryWrapper.orderByAsc("date"); // 按日期升序排列

        List<EbookSnapshot> snapshots = ebookSnapshotService.list(queryWrapper);

        if (snapshots == null || snapshots.isEmpty()) {
            return ResultUtil.data(new ArrayList<>());
        }

        // 计算增长数据
        List<EbookSnapshot> resultList = new ArrayList<>();

        for (int i = 0; i < snapshots.size(); i++) {
            EbookSnapshot currentSnapshot = snapshots.get(i);

            if (i == 0) {
                // 第一条记录，增长设为0
                currentSnapshot.setViewIncrease(0);
                currentSnapshot.setVoteIncrease(0);
            } else {
                // 计算与前一天的增长
                EbookSnapshot previousSnapshot = snapshots.get(i - 1);

                int viewIncrease = (currentSnapshot.getViewCount() != null ? currentSnapshot.getViewCount() : 0)
                                 - (previousSnapshot.getViewCount() != null ? previousSnapshot.getViewCount() : 0);
                int voteIncrease = (currentSnapshot.getVoteCount() != null ? currentSnapshot.getVoteCount() : 0)
                                 - (previousSnapshot.getVoteCount() != null ? previousSnapshot.getVoteCount() : 0);

                currentSnapshot.setViewIncrease(viewIncrease);
                currentSnapshot.setVoteIncrease(voteIncrease);
            }

            resultList.add(currentSnapshot);
        }

        return ResultUtil.data(resultList);
    }


}